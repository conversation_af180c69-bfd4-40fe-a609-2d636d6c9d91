import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/services/appwrite';
import { z } from 'zod';

// Validation schemas
const createUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(1, 'Name is required'),
  phone: z.string().optional(),
});

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

const updateUserSchema = z.object({
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  bio: z.string().optional(),
  company: z.string().optional(),
  location: z.string().optional(),
  website: z.string().url().optional(),
});

const passwordUpdateSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
});

const passwordResetSchema = z.object({
  email: z.string().email('Invalid email format'),
  url: z.string().url('Invalid URL format'),
});

// Helper function to extract session ID from headers
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

// Helper function to handle API responses
function handleResponse<T>(result: { success: boolean; data?: T; error?: any }) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString(),
    });
  } else {
    const statusCode = result.error?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Internal server error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        type: result.error?.type || 'server_error',
      },
      timestamp: new Date().toISOString(),
    }, { status: statusCode });
  }
}

// POST /api/appwrite/auth - Create user or login
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');

    switch (action) {
      case 'register': {
        const validatedData = createUserSchema.parse(body);
        const result = await authService.createUser(validatedData);
        return handleResponse(result);
      }

      case 'login': {
        const validatedData = loginSchema.parse(body);
        const result = await authService.loginUser(validatedData);
        
        if (result.success && result.data) {
          const response = handleResponse(result);
          
          // Set session cookie
          response.cookies.set('appwrite-session', result.data.session.sessionId, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 30 * 24 * 60 * 60, // 30 days
          });
          
          return response;
        }
        
        return handleResponse(result);
      }

      case 'logout': {
        const sessionId = getSessionId(request);
        if (!sessionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'No active session', code: 'NO_SESSION' }
          }, { status: 401 });
        }

        const result = await authService.logoutUser(sessionId);
        
        if (result.success) {
          const response = handleResponse(result);
          response.cookies.delete('appwrite-session');
          return response;
        }
        
        return handleResponse(result);
      }

      case 'password-reset': {
        const validatedData = passwordResetSchema.parse(body);
        const result = await authService.sendPasswordReset(validatedData);
        return handleResponse(result);
      }

      case 'verify-email': {
        const sessionId = getSessionId(request);
        const { secret } = body;
        
        if (!sessionId || !secret) {
          return NextResponse.json({
            success: false,
            error: { message: 'Session ID and secret are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await authService.verifyEmail(sessionId, secret);
        return handleResponse(result);
      }

      case 'send-verification': {
        const sessionId = getSessionId(request);
        const { url } = body;
        
        if (!sessionId || !url) {
          return NextResponse.json({
            success: false,
            error: { message: 'Session ID and URL are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await authService.sendEmailVerification(sessionId, url);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        }
      }, { status: 400 });
    }

    console.error('Auth API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// GET /api/appwrite/auth - Get current user or sessions
export async function GET(request: NextRequest) {
  try {
    const sessionId = getSessionId(request);
    const action = request.nextUrl.searchParams.get('action');

    if (!sessionId) {
      return NextResponse.json({
        success: false,
        error: { message: 'No active session', code: 'NO_SESSION' }
      }, { status: 401 });
    }

    switch (action) {
      case 'user':
      case null: {
        const result = await authService.getCurrentUser(sessionId);
        return handleResponse(result);
      }

      case 'sessions': {
        const result = await authService.getUserSessions(sessionId);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Auth GET API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// PUT /api/appwrite/auth - Update user or password
export async function PUT(request: NextRequest) {
  try {
    const sessionId = getSessionId(request);
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');

    if (!sessionId) {
      return NextResponse.json({
        success: false,
        error: { message: 'No active session', code: 'NO_SESSION' }
      }, { status: 401 });
    }

    switch (action) {
      case 'profile': {
        const validatedData = updateUserSchema.parse(body);
        const result = await authService.updateUser(sessionId, validatedData);
        return handleResponse(result);
      }

      case 'password': {
        const validatedData = passwordUpdateSchema.parse(body);
        const result = await authService.updatePassword(sessionId, validatedData);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        }
      }, { status: 400 });
    }

    console.error('Auth PUT API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// DELETE /api/appwrite/auth - Delete session
export async function DELETE(request: NextRequest) {
  try {
    const sessionId = getSessionId(request);
    const action = request.nextUrl.searchParams.get('action');
    const targetSessionId = request.nextUrl.searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({
        success: false,
        error: { message: 'No active session', code: 'NO_SESSION' }
      }, { status: 401 });
    }

    switch (action) {
      case 'session': {
        if (!targetSessionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Target session ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await authService.deleteSession(sessionId, targetSessionId);
        return handleResponse(result);
      }

      case 'all-sessions': {
        const result = await authService.deleteAllSessions(sessionId);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Auth DELETE API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}
