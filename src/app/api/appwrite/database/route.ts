import { NextRequest, NextResponse } from 'next/server';
import { databaseService } from '@/services/appwrite';
import { z } from 'zod';

// Validation schemas
const createDatabaseSchema = z.object({
  name: z.string().min(1, 'Database name is required'),
  databaseId: z.string().optional(),
});

const createCollectionSchema = z.object({
  databaseId: z.string().min(1, 'Database ID is required'),
  name: z.string().min(1, 'Collection name is required'),
  collectionId: z.string().optional(),
  permissions: z.array(z.string()).optional(),
  documentSecurity: z.boolean().optional(),
});

const createAttributeSchema = z.object({
  databaseId: z.string().min(1, 'Database ID is required'),
  collectionId: z.string().min(1, 'Collection ID is required'),
  key: z.string().min(1, 'Attribute key is required'),
  type: z.enum(['string', 'integer', 'float', 'boolean', 'datetime', 'email', 'ip', 'url']),
  size: z.number().optional(),
  required: z.boolean().optional(),
  default: z.any().optional(),
  array: z.boolean().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
});

const createIndexSchema = z.object({
  databaseId: z.string().min(1, 'Database ID is required'),
  collectionId: z.string().min(1, 'Collection ID is required'),
  key: z.string().min(1, 'Index key is required'),
  type: z.enum(['key', 'fulltext', 'unique']),
  attributes: z.array(z.string()).min(1, 'At least one attribute is required'),
  orders: z.array(z.string()).optional(),
});

const createDocumentSchema = z.object({
  databaseId: z.string().min(1, 'Database ID is required'),
  collectionId: z.string().min(1, 'Collection ID is required'),
  documentId: z.string().optional(),
  data: z.record(z.any()),
  permissions: z.array(z.string()).optional(),
});

const updateDocumentSchema = z.object({
  databaseId: z.string().min(1, 'Database ID is required'),
  collectionId: z.string().min(1, 'Collection ID is required'),
  documentId: z.string().min(1, 'Document ID is required'),
  data: z.record(z.any()),
  permissions: z.array(z.string()).optional(),
});

// Helper functions
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

function handleResponse<T>(result: { success: boolean; data?: T; error?: any }) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString(),
    });
  } else {
    const statusCode = result.error?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Internal server error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        type: result.error?.type || 'server_error',
      },
      timestamp: new Date().toISOString(),
    }, { status: statusCode });
  }
}

function getPaginationParams(request: NextRequest) {
  const url = new URL(request.url);
  return {
    limit: parseInt(url.searchParams.get('limit') || '25'),
    offset: parseInt(url.searchParams.get('offset') || '0'),
    cursor: url.searchParams.get('cursor') || undefined,
    orderBy: url.searchParams.get('orderBy') || undefined,
    orderDirection: (url.searchParams.get('orderDirection') as 'asc' | 'desc') || 'asc',
  };
}

// POST /api/appwrite/database - Create database, collection, attribute, index, or document
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);

    switch (action) {
      case 'database': {
        const validatedData = createDatabaseSchema.parse(body);
        const result = await databaseService.createDatabase(validatedData);
        return handleResponse(result);
      }

      case 'collection': {
        const validatedData = createCollectionSchema.parse(body);
        const result = await databaseService.createCollection(validatedData);
        return handleResponse(result);
      }

      case 'attribute': {
        const validatedData = createAttributeSchema.parse(body);
        const result = await databaseService.createAttribute(validatedData);
        return handleResponse(result);
      }

      case 'index': {
        const validatedData = createIndexSchema.parse(body);
        const result = await databaseService.createIndex(validatedData);
        return handleResponse(result);
      }

      case 'document': {
        const validatedData = createDocumentSchema.parse(body);
        const result = await databaseService.createDocument(validatedData, sessionId || undefined);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        }
      }, { status: 400 });
    }

    console.error('Database POST API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// GET /api/appwrite/database - Get database, collection, or list items
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);
    const pagination = getPaginationParams(request);

    switch (action) {
      case 'databases': {
        const result = await databaseService.listDatabases(pagination);
        return handleResponse(result);
      }

      case 'database': {
        const databaseId = url.searchParams.get('databaseId');
        if (!databaseId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.getDatabase(databaseId);
        return handleResponse(result);
      }

      case 'collections': {
        const databaseId = url.searchParams.get('databaseId');
        if (!databaseId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.listCollections(databaseId, pagination);
        return handleResponse(result);
      }

      case 'collection': {
        const databaseId = url.searchParams.get('databaseId');
        const collectionId = url.searchParams.get('collectionId');
        
        if (!databaseId || !collectionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID and Collection ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.getCollection(databaseId, collectionId);
        return handleResponse(result);
      }

      case 'documents': {
        const databaseId = url.searchParams.get('databaseId');
        const collectionId = url.searchParams.get('collectionId');
        
        if (!databaseId || !collectionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID and Collection ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        // Build query parameters
        const queryParams = {
          pagination,
          filters: {} as Record<string, any>,
          search: url.searchParams.get('search') || undefined,
        };

        // Extract filter parameters
        for (const [key, value] of url.searchParams.entries()) {
          if (key.startsWith('filter.')) {
            const filterKey = key.replace('filter.', '');
            queryParams.filters[filterKey] = value;
          }
        }

        const result = await databaseService.listDocuments(
          databaseId, 
          collectionId, 
          queryParams, 
          sessionId || undefined
        );
        return handleResponse(result);
      }

      case 'document': {
        const databaseId = url.searchParams.get('databaseId');
        const collectionId = url.searchParams.get('collectionId');
        const documentId = url.searchParams.get('documentId');
        
        if (!databaseId || !collectionId || !documentId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID, Collection ID, and Document ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.getDocument(
          databaseId, 
          collectionId, 
          documentId, 
          sessionId || undefined
        );
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Database GET API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// PUT /api/appwrite/database - Update document
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);

    switch (action) {
      case 'document': {
        const validatedData = updateDocumentSchema.parse(body);
        const result = await databaseService.updateDocument(validatedData, sessionId || undefined);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        }
      }, { status: 400 });
    }

    console.error('Database PUT API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// DELETE /api/appwrite/database - Delete database, collection, or document
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);

    switch (action) {
      case 'database': {
        const databaseId = url.searchParams.get('databaseId');
        if (!databaseId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.deleteDatabase(databaseId);
        return handleResponse(result);
      }

      case 'collection': {
        const databaseId = url.searchParams.get('databaseId');
        const collectionId = url.searchParams.get('collectionId');
        
        if (!databaseId || !collectionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID and Collection ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.deleteCollection(databaseId, collectionId);
        return handleResponse(result);
      }

      case 'document': {
        const databaseId = url.searchParams.get('databaseId');
        const collectionId = url.searchParams.get('collectionId');
        const documentId = url.searchParams.get('documentId');
        
        if (!databaseId || !collectionId || !documentId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Database ID, Collection ID, and Document ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await databaseService.deleteDocument(
          databaseId, 
          collectionId, 
          documentId, 
          sessionId || undefined
        );
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Database DELETE API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}
