import { 
  adminDatabases, 
  createSessionServices,
  AppwriteServerError,
  logger,
  ID,
  DATABASE_ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult, PaginationParams, PaginatedResult, QueryParams } from './base';

// Database interfaces
export interface DatabaseInfo {
  $id: string;
  name: string;
  $createdAt: string;
  $updatedAt: string;
}

export interface CollectionInfo {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  databaseId: string;
  name: string;
  enabled: boolean;
  documentSecurity: boolean;
  attributes: AttributeInfo[];
  indexes: IndexInfo[];
}

export interface AttributeInfo {
  key: string;
  type: 'string' | 'integer' | 'float' | 'boolean' | 'datetime' | 'email' | 'ip' | 'url';
  status: string;
  required: boolean;
  array?: boolean;
  size?: number;
  min?: number;
  max?: number;
  default?: any;
}

export interface IndexInfo {
  key: string;
  type: 'key' | 'fulltext' | 'unique';
  status: string;
  attributes: string[];
  orders?: string[];
}

export interface CreateDatabaseParams {
  name: string;
  databaseId?: string;
}

export interface CreateCollectionParams {
  databaseId: string;
  name: string;
  collectionId?: string;
  permissions?: string[];
  documentSecurity?: boolean;
}

export interface CreateAttributeParams {
  databaseId: string;
  collectionId: string;
  key: string;
  type: AttributeInfo['type'];
  size?: number;
  required?: boolean;
  default?: any;
  array?: boolean;
  min?: number;
  max?: number;
}

export interface CreateIndexParams {
  databaseId: string;
  collectionId: string;
  key: string;
  type: IndexInfo['type'];
  attributes: string[];
  orders?: string[];
}

export interface DocumentData {
  [key: string]: any;
}

export interface CreateDocumentParams {
  databaseId: string;
  collectionId: string;
  documentId?: string;
  data: DocumentData;
  permissions?: string[];
}

export interface UpdateDocumentParams {
  databaseId: string;
  collectionId: string;
  documentId: string;
  data: Partial<DocumentData>;
  permissions?: string[];
}

export interface QueryBuilder {
  equal(attribute: string, value: any): QueryBuilder;
  notEqual(attribute: string, value: any): QueryBuilder;
  lessThan(attribute: string, value: any): QueryBuilder;
  lessThanEqual(attribute: string, value: any): QueryBuilder;
  greaterThan(attribute: string, value: any): QueryBuilder;
  greaterThanEqual(attribute: string, value: any): QueryBuilder;
  search(attribute: string, value: string): QueryBuilder;
  isNull(attribute: string): QueryBuilder;
  isNotNull(attribute: string): QueryBuilder;
  between(attribute: string, start: any, end: any): QueryBuilder;
  startsWith(attribute: string, value: string): QueryBuilder;
  endsWith(attribute: string, value: string): QueryBuilder;
  select(attributes: string[]): QueryBuilder;
  orderAsc(attribute: string): QueryBuilder;
  orderDesc(attribute: string): QueryBuilder;
  limit(limit: number): QueryBuilder;
  offset(offset: number): QueryBuilder;
  build(): string[];
}

// Query builder implementation
class AppwriteQueryBuilder implements QueryBuilder {
  private queries: string[] = [];

  equal(attribute: string, value: any): QueryBuilder {
    this.queries.push(`equal("${attribute}", ${JSON.stringify(value)})`);
    return this;
  }

  notEqual(attribute: string, value: any): QueryBuilder {
    this.queries.push(`notEqual("${attribute}", ${JSON.stringify(value)})`);
    return this;
  }

  lessThan(attribute: string, value: any): QueryBuilder {
    this.queries.push(`lessThan("${attribute}", ${JSON.stringify(value)})`);
    return this;
  }

  lessThanEqual(attribute: string, value: any): QueryBuilder {
    this.queries.push(`lessThanEqual("${attribute}", ${JSON.stringify(value)})`);
    return this;
  }

  greaterThan(attribute: string, value: any): QueryBuilder {
    this.queries.push(`greaterThan("${attribute}", ${JSON.stringify(value)})`);
    return this;
  }

  greaterThanEqual(attribute: string, value: any): QueryBuilder {
    this.queries.push(`greaterThanEqual("${attribute}", ${JSON.stringify(value)})`);
    return this;
  }

  search(attribute: string, value: string): QueryBuilder {
    this.queries.push(`search("${attribute}", "${value}")`);
    return this;
  }

  isNull(attribute: string): QueryBuilder {
    this.queries.push(`isNull("${attribute}")`);
    return this;
  }

  isNotNull(attribute: string): QueryBuilder {
    this.queries.push(`isNotNull("${attribute}")`);
    return this;
  }

  between(attribute: string, start: any, end: any): QueryBuilder {
    this.queries.push(`between("${attribute}", ${JSON.stringify(start)}, ${JSON.stringify(end)})`);
    return this;
  }

  startsWith(attribute: string, value: string): QueryBuilder {
    this.queries.push(`startsWith("${attribute}", "${value}")`);
    return this;
  }

  endsWith(attribute: string, value: string): QueryBuilder {
    this.queries.push(`endsWith("${attribute}", "${value}")`);
    return this;
  }

  select(attributes: string[]): QueryBuilder {
    this.queries.push(`select([${attributes.map(attr => `"${attr}"`).join(', ')}])`);
    return this;
  }

  orderAsc(attribute: string): QueryBuilder {
    this.queries.push(`orderAsc("${attribute}")`);
    return this;
  }

  orderDesc(attribute: string): QueryBuilder {
    this.queries.push(`orderDesc("${attribute}")`);
    return this;
  }

  limit(limit: number): QueryBuilder {
    this.queries.push(`limit(${limit})`);
    return this;
  }

  offset(offset: number): QueryBuilder {
    this.queries.push(`offset(${offset})`);
    return this;
  }

  build(): string[] {
    return this.queries;
  }
}

// Database service class
export class DatabaseService extends BaseAppwriteService {
  constructor() {
    super('DatabaseService');
  }

  // Create query builder
  createQuery(): QueryBuilder {
    return new AppwriteQueryBuilder();
  }

  // Create database
  async createDatabase(params: CreateDatabaseParams): Promise<ServiceResult<DatabaseInfo>> {
    return this.executeOperation('createDatabase', async () => {
      this.validateRequired(params, ['name']);

      const database = await adminDatabases.create(
        params.databaseId || ID.unique(),
        params.name
      );

      logger.info(`Database created: ${database.$id}`);
      return database as DatabaseInfo;
    });
  }

  // Get database
  async getDatabase(databaseId: string): Promise<ServiceResult<DatabaseInfo>> {
    return this.executeOperation('getDatabase', async () => {
      this.validateRequired({ databaseId }, ['databaseId']);

      const database = await adminDatabases.get(databaseId);
      return database as DatabaseInfo;
    });
  }

  // List databases
  async listDatabases(pagination?: PaginationParams): Promise<ServiceResult<PaginatedResult<DatabaseInfo>>> {
    return this.executeOperation('listDatabases', async () => {
      const validatedPagination = this.validatePagination(pagination);
      
      const result = await adminDatabases.list(
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.databases as DatabaseInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Delete database
  async deleteDatabase(databaseId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteDatabase', async () => {
      this.validateRequired({ databaseId }, ['databaseId']);

      await adminDatabases.delete(databaseId);

      logger.info(`Database deleted: ${databaseId}`);
      return true;
    });
  }

  // Create collection
  async createCollection(params: CreateCollectionParams): Promise<ServiceResult<CollectionInfo>> {
    return this.executeOperation('createCollection', async () => {
      this.validateRequired(params, ['databaseId', 'name']);

      const collection = await adminDatabases.createCollection(
        params.databaseId,
        params.collectionId || ID.unique(),
        params.name,
        params.permissions,
        params.documentSecurity
      );

      logger.info(`Collection created: ${collection.$id} in database: ${params.databaseId}`);
      return collection as CollectionInfo;
    });
  }

  // Get collection
  async getCollection(databaseId: string, collectionId: string): Promise<ServiceResult<CollectionInfo>> {
    return this.executeOperation('getCollection', async () => {
      this.validateRequired({ databaseId, collectionId }, ['databaseId', 'collectionId']);

      const collection = await adminDatabases.getCollection(databaseId, collectionId);
      return collection as CollectionInfo;
    });
  }

  // List collections
  async listCollections(
    databaseId: string, 
    pagination?: PaginationParams
  ): Promise<ServiceResult<PaginatedResult<CollectionInfo>>> {
    return this.executeOperation('listCollections', async () => {
      this.validateRequired({ databaseId }, ['databaseId']);
      const validatedPagination = this.validatePagination(pagination);

      const result = await adminDatabases.listCollections(
        databaseId,
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.collections as CollectionInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Delete collection
  async deleteCollection(databaseId: string, collectionId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteCollection', async () => {
      this.validateRequired({ databaseId, collectionId }, ['databaseId', 'collectionId']);

      await adminDatabases.deleteCollection(databaseId, collectionId);

      logger.info(`Collection deleted: ${collectionId} from database: ${databaseId}`);
      return true;
    });
  }

  // Create attribute
  async createAttribute(params: CreateAttributeParams): Promise<ServiceResult<AttributeInfo>> {
    return this.executeOperation('createAttribute', async () => {
      this.validateRequired(params, ['databaseId', 'collectionId', 'key', 'type']);

      let attribute;
      switch (params.type) {
        case 'string':
          attribute = await adminDatabases.createStringAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.size || 255,
            params.required || false,
            params.default,
            params.array || false
          );
          break;
        case 'integer':
          attribute = await adminDatabases.createIntegerAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.min,
            params.max,
            params.default,
            params.array || false
          );
          break;
        case 'float':
          attribute = await adminDatabases.createFloatAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.min,
            params.max,
            params.default,
            params.array || false
          );
          break;
        case 'boolean':
          attribute = await adminDatabases.createBooleanAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.default,
            params.array || false
          );
          break;
        case 'datetime':
          attribute = await adminDatabases.createDatetimeAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.default,
            params.array || false
          );
          break;
        case 'email':
          attribute = await adminDatabases.createEmailAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.default,
            params.array || false
          );
          break;
        case 'ip':
          attribute = await adminDatabases.createIpAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.default,
            params.array || false
          );
          break;
        case 'url':
          attribute = await adminDatabases.createUrlAttribute(
            params.databaseId,
            params.collectionId,
            params.key,
            params.required || false,
            params.default,
            params.array || false
          );
          break;
        default:
          throw new AppwriteServerError(
            `Unsupported attribute type: ${params.type}`,
            'INVALID_ATTRIBUTE_TYPE',
            'validation_error',
            400
          );
      }

      logger.info(`Attribute created: ${params.key} in collection: ${params.collectionId}`);
      return attribute as AttributeInfo;
    });
  }

  // Create index
  async createIndex(params: CreateIndexParams): Promise<ServiceResult<IndexInfo>> {
    return this.executeOperation('createIndex', async () => {
      this.validateRequired(params, ['databaseId', 'collectionId', 'key', 'type', 'attributes']);

      const index = await adminDatabases.createIndex(
        params.databaseId,
        params.collectionId,
        params.key,
        params.type,
        params.attributes,
        params.orders
      );

      logger.info(`Index created: ${params.key} in collection: ${params.collectionId}`);
      return index as IndexInfo;
    });
  }

  // Create document
  async createDocument(params: CreateDocumentParams, sessionId?: string): Promise<ServiceResult<DocumentData>> {
    return this.executeOperation('createDocument', async () => {
      this.validateRequired(params, ['databaseId', 'collectionId', 'data']);

      const sanitizedData = this.sanitizeInput(params.data);
      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      const document = await services.databases.createDocument(
        params.databaseId,
        params.collectionId,
        params.documentId || ID.unique(),
        sanitizedData,
        params.permissions
      );

      logger.info(`Document created: ${document.$id} in collection: ${params.collectionId}`);
      return document as DocumentData;
    });
  }

  // Get document
  async getDocument(
    databaseId: string,
    collectionId: string,
    documentId: string,
    sessionId?: string
  ): Promise<ServiceResult<DocumentData>> {
    return this.executeOperation('getDocument', async () => {
      this.validateRequired({ databaseId, collectionId, documentId }, ['databaseId', 'collectionId', 'documentId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
      const document = await services.databases.getDocument(databaseId, collectionId, documentId);

      return document as DocumentData;
    });
  }

  // List documents
  async listDocuments(
    databaseId: string,
    collectionId: string,
    queryParams?: QueryParams,
    sessionId?: string
  ): Promise<ServiceResult<PaginatedResult<DocumentData>>> {
    return this.executeOperation('listDocuments', async () => {
      this.validateRequired({ databaseId, collectionId }, ['databaseId', 'collectionId']);

      const validatedPagination = this.validatePagination(queryParams?.pagination);
      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Build queries
      let queries: string[] = [];
      if (queryParams?.filters) {
        const queryBuilder = this.createQuery();
        Object.entries(queryParams.filters).forEach(([key, value]) => {
          queryBuilder.equal(key, value);
        });
        queries = queryBuilder.build();
      }

      // Add pagination
      if (validatedPagination.limit) {
        queries.push(`limit(${validatedPagination.limit})`);
      }
      if (validatedPagination.offset) {
        queries.push(`offset(${validatedPagination.offset})`);
      }

      const result = await services.databases.listDocuments(
        databaseId,
        collectionId,
        queries
      );

      const paginatedResult = this.formatPaginatedResult(
        result.documents as DocumentData[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Update document
  async updateDocument(params: UpdateDocumentParams, sessionId?: string): Promise<ServiceResult<DocumentData>> {
    return this.executeOperation('updateDocument', async () => {
      this.validateRequired(params, ['databaseId', 'collectionId', 'documentId', 'data']);

      const sanitizedData = this.sanitizeInput(params.data);
      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      const document = await services.databases.updateDocument(
        params.databaseId,
        params.collectionId,
        params.documentId,
        sanitizedData,
        params.permissions
      );

      logger.info(`Document updated: ${params.documentId} in collection: ${params.collectionId}`);
      return document as DocumentData;
    });
  }

  // Delete document
  async deleteDocument(
    databaseId: string,
    collectionId: string,
    documentId: string,
    sessionId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteDocument', async () => {
      this.validateRequired({ databaseId, collectionId, documentId }, ['databaseId', 'collectionId', 'documentId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
      await services.databases.deleteDocument(databaseId, collectionId, documentId);

      logger.info(`Document deleted: ${documentId} from collection: ${collectionId}`);
      return true;
    });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminDatabases.list();
      return true;
    } catch (error) {
      logger.error('Database service health check failed', error);
      return false;
    }
  }
}
