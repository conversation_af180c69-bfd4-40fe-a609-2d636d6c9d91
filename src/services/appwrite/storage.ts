import { 
  adminStorage, 
  createSessionServices,
  AppwriteServerError,
  logger,
  ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult, PaginationParams, PaginatedResult } from './base';

// Storage interfaces
export interface BucketInfo {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  fileSecurity: boolean;
  name: string;
  enabled: boolean;
  maximumFileSize: number;
  allowedFileExtensions: string[];
  compression: string;
  encryption: boolean;
  antivirus: boolean;
}

export interface FileInfo {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  name: string;
  signature: string;
  mimeType: string;
  sizeOriginal: number;
  chunksTotal: number;
  chunksUploaded: number;
}

export interface CreateBucketParams {
  name: string;
  bucketId?: string;
  permissions?: string[];
  fileSecurity?: boolean;
  enabled?: boolean;
  maximumFileSize?: number;
  allowedFileExtensions?: string[];
  compression?: 'none' | 'gzip' | 'zstd';
  encryption?: boolean;
  antivirus?: boolean;
}

export interface UpdateBucketParams {
  bucketId: string;
  name?: string;
  permissions?: string[];
  fileSecurity?: boolean;
  enabled?: boolean;
  maximumFileSize?: number;
  allowedFileExtensions?: string[];
  compression?: 'none' | 'gzip' | 'zstd';
  encryption?: boolean;
  antivirus?: boolean;
}

export interface UploadFileParams {
  bucketId: string;
  fileId?: string;
  file: File | Buffer;
  permissions?: string[];
  onProgress?: (progress: { $id: string; progress: number; sizeUploaded: number; chunksTotal: number; chunksUploaded: number }) => void;
}

export interface FileUploadResult {
  file: FileInfo;
  url: string;
  preview?: string;
}

export interface ImageTransformOptions {
  width?: number;
  height?: number;
  gravity?: 'center' | 'top-left' | 'top' | 'top-right' | 'left' | 'right' | 'bottom-left' | 'bottom' | 'bottom-right';
  quality?: number;
  borderWidth?: number;
  borderColor?: string;
  borderRadius?: number;
  opacity?: number;
  rotation?: number;
  background?: string;
  output?: 'jpg' | 'jpeg' | 'png' | 'gif' | 'webp';
}

// Storage service class
export class StorageService extends BaseAppwriteService {
  constructor() {
    super('StorageService');
  }

  // Create bucket
  async createBucket(params: CreateBucketParams): Promise<ServiceResult<BucketInfo>> {
    return this.executeOperation('createBucket', async () => {
      this.validateRequired(params, ['name']);

      const bucket = await adminStorage.createBucket(
        params.bucketId || ID.unique(),
        params.name,
        params.permissions,
        params.fileSecurity,
        params.enabled,
        params.maximumFileSize,
        params.allowedFileExtensions,
        params.compression,
        params.encryption,
        params.antivirus
      );

      logger.info(`Bucket created: ${bucket.$id}`);
      return bucket as BucketInfo;
    });
  }

  // Get bucket
  async getBucket(bucketId: string): Promise<ServiceResult<BucketInfo>> {
    return this.executeOperation('getBucket', async () => {
      this.validateRequired({ bucketId }, ['bucketId']);

      const bucket = await adminStorage.getBucket(bucketId);
      return bucket as BucketInfo;
    });
  }

  // List buckets
  async listBuckets(pagination?: PaginationParams): Promise<ServiceResult<PaginatedResult<BucketInfo>>> {
    return this.executeOperation('listBuckets', async () => {
      const validatedPagination = this.validatePagination(pagination);

      const result = await adminStorage.listBuckets(
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.buckets as BucketInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Update bucket
  async updateBucket(params: UpdateBucketParams): Promise<ServiceResult<BucketInfo>> {
    return this.executeOperation('updateBucket', async () => {
      this.validateRequired(params, ['bucketId']);

      const bucket = await adminStorage.updateBucket(
        params.bucketId,
        params.name,
        params.permissions,
        params.fileSecurity,
        params.enabled,
        params.maximumFileSize,
        params.allowedFileExtensions,
        params.compression,
        params.encryption,
        params.antivirus
      );

      logger.info(`Bucket updated: ${params.bucketId}`);
      return bucket as BucketInfo;
    });
  }

  // Delete bucket
  async deleteBucket(bucketId: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteBucket', async () => {
      this.validateRequired({ bucketId }, ['bucketId']);

      await adminStorage.deleteBucket(bucketId);

      logger.info(`Bucket deleted: ${bucketId}`);
      return true;
    });
  }

  // Upload file
  async uploadFile(params: UploadFileParams, sessionId?: string): Promise<ServiceResult<FileUploadResult>> {
    return this.executeOperation('uploadFile', async () => {
      this.validateRequired(params, ['bucketId', 'file']);

      const services = sessionId ? createSessionServices(sessionId) : { storage: adminStorage };
      const fileId = params.fileId || ID.unique();

      const file = await services.storage.createFile(
        params.bucketId,
        fileId,
        params.file,
        params.permissions,
        params.onProgress
      );

      // Generate file URL
      const fileUrl = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${params.bucketId}/files/${file.$id}/view`;
      
      // Generate preview URL for images
      let previewUrl: string | undefined;
      if (file.mimeType.startsWith('image/')) {
        previewUrl = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${params.bucketId}/files/${file.$id}/preview`;
      }

      logger.info(`File uploaded: ${file.$id} to bucket: ${params.bucketId}`);
      
      return {
        file: file as FileInfo,
        url: fileUrl,
        preview: previewUrl
      };
    });
  }

  // Get file
  async getFile(bucketId: string, fileId: string, sessionId?: string): Promise<ServiceResult<FileInfo>> {
    return this.executeOperation('getFile', async () => {
      this.validateRequired({ bucketId, fileId }, ['bucketId', 'fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { storage: adminStorage };
      const file = await services.storage.getFile(bucketId, fileId);

      return file as FileInfo;
    });
  }

  // List files
  async listFiles(
    bucketId: string,
    pagination?: PaginationParams,
    sessionId?: string
  ): Promise<ServiceResult<PaginatedResult<FileInfo>>> {
    return this.executeOperation('listFiles', async () => {
      this.validateRequired({ bucketId }, ['bucketId']);

      const validatedPagination = this.validatePagination(pagination);
      const services = sessionId ? createSessionServices(sessionId) : { storage: adminStorage };

      const result = await services.storage.listFiles(
        bucketId,
        undefined, // queries
        validatedPagination.limit,
        validatedPagination.offset
      );

      const paginatedResult = this.formatPaginatedResult(
        result.files as FileInfo[],
        result.total,
        validatedPagination
      );

      return paginatedResult;
    });
  }

  // Get file download URL
  async getFileDownload(bucketId: string, fileId: string): Promise<ServiceResult<string>> {
    return this.executeOperation('getFileDownload', async () => {
      this.validateRequired({ bucketId, fileId }, ['bucketId', 'fileId']);

      const url = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${bucketId}/files/${fileId}/download`;
      return url;
    });
  }

  // Get file view URL
  async getFileView(bucketId: string, fileId: string): Promise<ServiceResult<string>> {
    return this.executeOperation('getFileView', async () => {
      this.validateRequired({ bucketId, fileId }, ['bucketId', 'fileId']);

      const url = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${bucketId}/files/${fileId}/view`;
      return url;
    });
  }

  // Get file preview URL with transformations
  async getFilePreview(
    bucketId: string, 
    fileId: string, 
    transformOptions?: ImageTransformOptions
  ): Promise<ServiceResult<string>> {
    return this.executeOperation('getFilePreview', async () => {
      this.validateRequired({ bucketId, fileId }, ['bucketId', 'fileId']);

      let url = `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${bucketId}/files/${fileId}/preview`;
      
      if (transformOptions) {
        const params = new URLSearchParams();
        
        Object.entries(transformOptions).forEach(([key, value]) => {
          if (value !== undefined) {
            params.append(key, value.toString());
          }
        });

        if (params.toString()) {
          url += `?${params.toString()}`;
        }
      }

      return url;
    });
  }

  // Delete file
  async deleteFile(bucketId: string, fileId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteFile', async () => {
      this.validateRequired({ bucketId, fileId }, ['bucketId', 'fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { storage: adminStorage };
      await services.storage.deleteFile(bucketId, fileId);

      logger.info(`File deleted: ${fileId} from bucket: ${bucketId}`);
      return true;
    });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminStorage.listBuckets();
      return true;
    } catch (error) {
      logger.error('Storage service health check failed', error);
      return false;
    }
  }
}
